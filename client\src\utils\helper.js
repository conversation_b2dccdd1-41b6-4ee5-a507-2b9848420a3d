import { IMAGE_BASE_URL } from 'config';
import { get, isEmpty, isEqual, sortBy } from 'lodash';
import moment from 'moment';
import { ALERT_SUCCESS } from 'redux/reducers/alertReducer';
import { logoutSuccess, storeMiqaatAndArazCityAction, storePermissionToken, storeSelectedModule } from 'redux/reducers/loginReducer';
import { getUserDetail } from './auth';
import NoImagePlaceHolder from '../assets/images/users/No-Image-Placeholder.svg.png';
import { ADMIN, ID_8_DIGITS_REGEX, MiqaatAndArazCityLocalStorageKey, MiqaatLocalStorageKey, SUPERADMIN } from './constant';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { addAttachmentAction } from 'redux/actions/inboxAction';
import AudioIcon from '../assets/images/AudioIcon.png';
import DocIcon from '../assets/images/doc.png';
import DocsIcon from '../assets/images/google-docs.png';
import GoogleSheetIcon from '../assets/images/google-sheets.png';
import PDFIcon from '../assets/images/pdfIcon.png';
import ImageIcon from '../assets/images/photo.png';
import FileIcon from '../assets/images/pdfIcon.png';
import VideoIcon from '../assets/images/youtube.png';
import CSVIcon from '../assets/images/csv-file.png';
import { GOOGLE_MAP_API_KEY } from 'config';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import PPTIcon from '../assets/images/PPTIcon.jpg';

dayjs.extend(relativeTime);
export const getItemFromLocalStorage = (key, defaultValue = []) => {
  try {
    if (!localStorage.hasOwnProperty(key)) {
      return defaultValue;
    }
    const item = localStorage.getItem(key);
    return JSON.parse(item);
  } catch (error) {
    return [];
  }
};

export const imageOnError = (event) => {
  event.target.src = NoImagePlaceHolder;
};

export const setItemToLocalStorage = (key, value) => {
  try {
    if (value) {
      localStorage.setItem(key, JSON.stringify(value));
    }
  } catch (error) {}
};

export const removeItemFromLocalStorage = (key) => {
  try {
    if (!localStorage.hasOwnProperty(key)) {
      return;
    }
    localStorage.removeItem(key);
  } catch (error) {}
};

export const convertDateToStringFormat = (date, year, showToday) => {
  if (date) {
    const today = moment().startOf('day');
    const inputDate = moment(date).startOf('day');

    if (showToday && inputDate.isSame(today)) {
      return 'Today';
    }

    if (year) {
      return moment(date).format('YYYY-MM-DD');
    }
    return moment(date).format('DD/MM/YYYY');
  }
  return '';
};

export const getDateWithTime = (dateString) => {
  if (!dateString) return '';
  const localDate = moment.utc(dateString).local(); // ⬅️ correct conversion
  return localDate.format('MMM D, YYYY, h:mm A');
};

export const formatRelativeTime = (timestamp) => {
  const now = dayjs();
  const createdAt = dayjs(timestamp);
  const diffInMinutes = now.diff(createdAt, 'minute');
  const diffInHours = now.diff(createdAt, 'hour');
  const diffInDays = now.diff(createdAt, 'day');

  if (diffInMinutes === 0) {
    return 'Just now';
  } else if (diffInMinutes < 60) {
    return `${diffInMinutes} min ago`;
  } else if (diffInHours < 12) {
    return `${diffInHours} hr ago`;
  } else if (diffInHours < 24) {
    return '1 day ago';
  } else if (diffInDays <= 1) {
    return `${diffInDays} day ago`;
  } else {
    return createdAt.format('DD MMM');
  }
};

export const formatNumber = (value) => (value && !isNaN(value) ? parseFloat(value) : '0');

export const formattedPrice = (price) => {
  let value = formatNumber(price);
  if (value) {
    if (typeof value === 'string') {
      value = parseFloat(value);
    }

    return value?.toString()?.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  } else {
    return value;
  }
};

export const clearUserPermissions = (dispatch, data) => {
  let { preserveMultipleMiqaat } = data || {};
  dispatch(storeSelectedModule(''));
  dispatch(storeMiqaatAndArazCityAction({}));
  dispatch(storePermissionToken(''));
  removeItemFromLocalStorage('permissionToken');
  removeItemFromLocalStorage('permissionTokenID');
  removeItemFromLocalStorage('selectedModule');
  removeItemFromLocalStorage('isSystemUser');
  removeItemFromLocalStorage(MiqaatAndArazCityLocalStorageKey);
  if (!preserveMultipleMiqaat) {
    removeItemFromLocalStorage('multipleMiqaat');
  }
  removeItemFromLocalStorage(MiqaatLocalStorageKey);
};

export const logoutUser = (dispatch, navigate) => {
  removeItemFromLocalStorage('token');
  clearUserPermissions(dispatch);
  dispatch(logoutSuccess());
  if (navigate) {
    navigate('/login');
  } else {
    window.location.href = '/login';
  }
};

export const notification = (success, message, error = false) => ({
  type: ALERT_SUCCESS,
  payload: { success, message, error }
});

export const showAlert = (dispatch, success, message, error = false, warning = false) => {
  dispatch({
    type: ALERT_SUCCESS,
    payload: { success, message, error, warning }
  });
};

export const badgeColor = (color) => {
  if (color === 'Done') return 'success';
  else return 'warning';
};

export const getSupplierName = (supplier, supplierList, val) => {
  const selectedSupplier = supplierList?.filter((supp) => supp?.value == supplier?.supplier);

  if (selectedSupplier && selectedSupplier?.length > 0) {
    return selectedSupplier[0]?.label;
  } else {
    return '';
  }
};

export const generateUniqueId = () => {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 1000);
  return `${timestamp}-${random}`;
};

export const compareTwoArrayOfObjects = (firstArrayOfObjects, secondArrayOfObjects, specificKey = null) => {
  if (specificKey) {
    return isEqual(
      firstArrayOfObjects?.map((item) => item[specificKey]),
      secondArrayOfObjects?.map((item) => item[specificKey])
    );
  } else {
    // Compare based on all keys
    return isEqual(
      sortBy(firstArrayOfObjects, Object.keys(firstArrayOfObjects[0])),
      sortBy(secondArrayOfObjects, Object.keys(secondArrayOfObjects[0]))
    );
  }
};

export const updateFields = (fields, options, targetFieldName, key, addAll = true) => {
  return fields?.map((field) => {
    if (field.name === targetFieldName) {
      let updateOptions = options?.map((option) => ({
        label: option[key],
        value: option?._id
      }));
      return {
        ...field,
        items: field?.multiple && updateOptions?.length > 0 && addAll ? [{ label: 'All', value: 'all' }, ...updateOptions] : updateOptions
      };
    }
    return field;
  });
};

export const getImageWithBaseUrl = (image) => {
  if (typeof image === 'string') return image?.startsWith('blob') || image?.startsWith('https') ? image : IMAGE_BASE_URL + image;
  else return '';
};

const isArrayOfObjects = (arr) => {
  return Array.isArray(arr) && arr.every((item) => typeof item === 'object' && item !== null && !Array.isArray(item));
};

export const getFieldValues = (dataObject, modifiedFieldValue) => {
  return Object.keys(dataObject)
    .filter((key) => {
      if (isArrayOfObjects(modifiedFieldValue)) {
        return modifiedFieldValue.some((field) => field?.name === key);
      } else {
        return modifiedFieldValue.includes(key);
      }
    })
    .reduce((obj, key) => {
      obj[key] = processField(key, dataObject[key]);
      return obj;
    }, {});
};

export const processField = (key, value) => {
  switch (key) {
    case 'jamiats':
    case 'jamaats':
      return value || [];
    case 'miqaatID':
      return value?._id;
    default:
      return value;
  }
};

export const updateInputFields = (fields, value, targetFieldName, key, valueKey) => {
  return fields?.map((field) => {
    let { addAllOption, multiple, name, type } = field || {};
    switch (type) {
      case 'select':
      case 'autoselect':
        if (name === targetFieldName) {
          let updateOptions =
            Array.isArray(value) &&
            value?.map((option) => ({
              label: option[key],
              value: valueKey ? option?.[valueKey] : option?._id
            }));
          return {
            ...field,
            items:
              addAllOption && multiple && updateOptions?.length > 0 ? [{ label: 'All', value: 'all' }, ...updateOptions] : updateOptions
          };
        }
        break;
      case 'text':
      case 'switch':
        if (name === targetFieldName) {
          return {
            ...field,
            [key]: value
          };
        }
        break;

      default:
        break;
    }

    return field;
  });
};

export const hasPermission = (module, action, functionName) => {
  let user = getUserDetail();
  let selectedModule = getItemFromLocalStorage('selectedModule', '');
  if (module === 'superAdmin') return true;
  if (selectedModule) {
    let permission = user?.permissions[selectedModule]?.[module]?.[action];

    if (typeof permission == 'object') {
      return permission?.permission && permission?.subPermission?.find((func) => func?.uniqueName === functionName)?.permission;
    }
    return user?.permissions[selectedModule]?.[module]?.[action] || false;
  } else {
    return false;
  }
};

export const getPageUrl = (baseUrl, endpoints) => {
  return !endpoints ? `${baseUrl}/list` : `${baseUrl}/${endpoints}`;
};
export const addPageUrl = (baseUrl, endpoints) => {
  return !endpoints ? `${baseUrl}/add` : `${baseUrl}/add/${endpoints}`;
};
export const editPageUrl = (baseUrl, data, endpoints) => {
  return !endpoints ? `${baseUrl}/edit/${data}` : `${baseUrl}/edit/${endpoints}/${data}`;
};
export const viewPageUrl = (baseUrl, data, endpoints) => {
  return !endpoints ? `${baseUrl}/view/${data}` : `${baseUrl}/view/${endpoints}/${data}`;
};

export const capitalizeFirstLetter = (string) => {
  if (typeof string === 'number') return string;
  if (typeof string !== 'string' || !string) return '-';

  return string.charAt(0).toUpperCase() + string.slice(1);
};

export const getOptions = (options, label, value, multiple) => {
  if (!Array.isArray(options) || isEmpty(options)) {
    return [];
  }

  const mappedOptions = options.map((option) => ({
    label: option[label],
    value: value ? option[value] : option?._id
  }));

  return multiple ? [{ label: 'All', value: 'all' }, ...mappedOptions] : mappedOptions;
};

export const isPermissionAvailable = () => {
  let permissions = getItemFromLocalStorage('permissionToken', '');
  return (permissions && !isEmpty(getUserDetail()?.selectedModulePermissions)) || false;
};

export const generateFileId = () => {
  return `file-${Math.floor(Date.now() / 1000)}-${Math.random().toString(36).substr(2, 5)}`;
};

export const uploadFiles = async (files, awsGroupID, action, dispatch, key, payload) => {
  const formData = new FormData();
  if (awsGroupID) formData.append('awsGroupID', awsGroupID);

  files.forEach((file) => formData.append(key || 'files', file.file));

  let response = {};
  if (payload) {
    response = await dispatch(action({ ...payload, formData }));
  } else {
    response = await dispatch(action(formData));
  }

  if (!response?.payload?.success) {
    dispatch(notification(false, response?.payload?.message, true));
  }
  return response;
};

export const addAttachments = async (files, awsGroupID, dispatch) => {
  if (!files?.length) return { payload: { success: false } };

  const formData = new FormData();
  if (awsGroupID) formData.append('awsGroupID', awsGroupID);

  files.forEach((file) => formData.append('files', file.file));

  return await dispatch(addAttachmentAction(formData));
};

export const checkIsUserSuperAdmin = () => {
  const userDetail = getUserDetail();
  const isSystemUser = getItemFromLocalStorage('isSystemUser', false);
  return userDetail?.role === SUPERADMIN || isSystemUser;
};

export const getAttachmentIcon = (attachment) => {
  if (!attachment?.fileType) return FileIcon;

  const fileType = attachment?.fileType?.toLowerCase();
  if (fileType?.startsWith('application/pdf')) return PDFIcon;
  if (
    fileType?.startsWith('application/msword') ||
    fileType?.startsWith('application/vnd.openxmlformats-officedocument.wordprocessingml.document') ||
    fileType?.startsWith('application/vnd.openxmlformats-officedocument.wordprocessingml.document')
  )
    return DocsIcon;
  if (
    fileType?.startsWith('application/vnd.ms-powerpoint') ||
    fileType?.startsWith('application/vnd.openxmlformats-officedocument.presentationml.presentation')
  )
    return PPTIcon;
  if (fileType?.startsWith('image/')) return ImageIcon;
  if (fileType?.startsWith('audio/')) return AudioIcon;
  if (fileType?.startsWith('video/')) return VideoIcon;
  if (fileType?.startsWith('text/csv')) return CSVIcon;
  if (
    fileType?.startsWith('xlsx') ||
    fileType?.startsWith('xls') ||
    fileType?.startsWith('application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
  )
    return GoogleSheetIcon;

  return FileIcon;
};

export const isConsentAccepeted = () => {
  let isSystemUser = getItemFromLocalStorage('isSystemUser', false);
  let selectedMiqaatAndArazCity = getItemFromLocalStorage(MiqaatLocalStorageKey, {});
  const { miqaats, role } = getUserDetail();
  let selectedMiqaat = miqaats?.find(
    (miqaat) =>
      miqaat?.miqaatID?._id === selectedMiqaatAndArazCity?.miqaat && miqaat?.arazCityID?._id === selectedMiqaatAndArazCity?.arazCity
  );
  if (isSystemUser || role === SUPERADMIN || role === ADMIN) return true;
  else return selectedMiqaat?.consentRequired && selectedMiqaat?.consentAccepted ? true : !selectedMiqaat?.consentRequired ? true : false;
};

const HigherPositionIDs = [
  '678ca82d4a83be4c23390284',
  '678ca7ce4a83be4c2339027c',
  '678ca8684a83be4c23390298',
  '678ca8804a83be4c233902a2',
  '678ca84a4a83be4c2339028e'
];

export const CheckPositionForConsent = (position) => {
  return !HigherPositionIDs?.includes(position?._id || position?.id);
};

const HigherPositionRoleIDs = [
  '678ca7ce4a83be4c2339027c',
  '678ca84a4a83be4c2339028e',
  '678ca8684a83be4c23390298',
  '678ca8804a83be4c233902a2',
  '678ca8d64a83be4c233902ac',
  '678ca9454a83be4c233902c0',
  '678ca95d4a83be4c233902ca',
  '678cafae4a83be4c2339039d',
  '678cb1454a83be4c233903b6',
  '678ca82d4a83be4c23390285',
  '681b0354a892ac1747e332a2',
  '681b52ff085b902c11eb6b52',
  '681b5324085b902c11eb8233',
  '681b5455085b902c11eb9e4f',
  '681c60f2c8644991102b85ed'
];

export const CheckPositionRoleForConsent = (role) => {
  return !HigherPositionRoleIDs?.includes(role);
};

export const showMiqaatValidationAlert = (headerFieldValue, dispatch) => {
  const { miqaat, arazCity } = headerFieldValue || {};
  let message = '';
  if (!miqaat) {
    message = 'Please select Miqaat';
  } else if (!arazCity) {
    message = 'Please select Araz City';
  }
  dispatch(notification(false, message, true));
};

export const generateMapLink = (lat, lng, dispatch) => {
  if (lat && lng) {
    return `https://www.google.com/maps/search/?api=1&query=${lat},${lng}`;
  } else {
    dispatch(notification(false, 'Please enter both latitude and longitude', true));
    return '';
  }
};

export const convertToCamelCase = (str) => {
  if (!str) return '';

  return str
    .replace(/[^a-zA-Z0-9 ]+/g, '')
    .trim()
    .split(/\s+/)
    .map((word, index) => (index === 0 ? word.toLowerCase() : word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()))
    .join('');
};

export const extractLatLng = async (url, dispatch) => {
  try {
    let apiUrl = `https://maps.googleapis.com/maps/api/geocode/json?place_id=x1hsNpaH6F9MFbXV7&key=${GOOGLE_MAP_API_KEY}`;
    const response = await fetch(`${apiUrl}`);
    const regex = /@([-.\d]+),([-.\d]+)/;
    const match = url.match(regex);

    if (match) {
      return { lat: match[1], lng: match[2] };
    } else {
      const searchParams = new URL(url).searchParams;
      const query = searchParams.get('query');
      if (query) {
        const [lat, lng] = query.split(',');
        return { lat, lng };
      } else {
        // dispatch(notification(false, 'Invalid Google Maps URL', true));
        return { lat: '', lng: '' };
      }
    }
  } catch (error) {
    // dispatch(notification(false, 'Error parsing URL', true));
    return { lat: '', lng: '' };
  }
};

export const generatePDF = async (contentRef, fileName, setLoading) => {
  if (!contentRef?.current) return;

  try {
    setLoading && setLoading(true);

    setTimeout(async () => {
      if (!contentRef.current) {
        setLoading && setLoading(false);
        return;
      }

      const pdf = new jsPDF('p', 'mm', 'a4');
      const padding = 10;

      try {
        const canvas = await html2canvas(contentRef.current, { scale: 2 });
        const imgData = canvas.toDataURL('image/png');
        const imgProps = pdf.getImageProperties(imgData);
        const pdfWidth = pdf.internal.pageSize.getWidth() - 2 * padding;
        const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;

        let heightLeft = pdfHeight;
        let position = padding;

        pdf.addImage(imgData, 'PNG', padding, position, pdfWidth, pdfHeight);
        heightLeft -= pdf.internal.pageSize.getHeight() - 2 * padding;

        while (heightLeft > 0) {
          position = heightLeft - pdfHeight + padding;
          pdf.addPage();
          pdf.addImage(imgData, 'PNG', padding, position, pdfWidth, pdfHeight);
          heightLeft -= pdf.internal.pageSize.getHeight() - 2 * padding;
        }

        pdf.save(`${fileName}.pdf`);
      } catch (err) {
        console.error('Error generating PDF:', err);
      }

      setLoading && setLoading(false);
    }, 500);
  } catch (error) {
    console.error('Error in generatePDF:', error);
    setLoading && setLoading(false);
  }
};

export const handleDownload = (rowData, dispatch) => {
  const fileUrl = rowData?.fileUrl;
  const fileName = rowData?.name || 'file.pdf';

  if (!fileUrl) {
    dispatch(notification(false, 'No file available for download.', true));
    return;
  }

  const link = document.createElement('a');
  link.href = fileUrl;
  link.setAttribute('download', fileName);
  link.setAttribute('target', '_blank');
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

export const validateFields = (fields, data, dispatch) => {
  for (let field of fields) {
    const { type, name, label } = field;
    const value = data[name];

    if (value === undefined || value === null || value === '') {
      dispatch(notification(false, `${label || name} is required.`, true));
      return false;
    }

    if (type === 'number' && isNaN(value)) {
      dispatch(notification(false, `${label || name} must be a valid number.`, true));
      return false;
    }
  }

  return true;
};

export const updateDrawingFiles = (drawingModal, name, files, module, action) => {
  const updatedData = drawingModal?.map((item) => {
    if (item.name === name) {
      return {
        ...item,
        uploadPermission: hasPermission(module, 'upload', item?.name),
        approvePermission: hasPermission(module, 'approve', item?.name),
        files: files
      };
    }
    return item;
  });

  return updatedData;
};

export const showAlertMessage = (miqaatAndArazCity, dispatch) => {
  const { miqaat, arazCity } = miqaatAndArazCity || {};
  let message = '';
  if (!miqaat) {
    message = 'Please select Miqaat';
  } else if (!arazCity) {
    message = 'Please select Araz City';
  }
  dispatch(notification(false, message, true));
};

export const prettifyKey = (key) =>
  key
    .replace(/ID$/, '')
    .replace(/([a-z])([A-Z])/g, '$1 $2')
    .replace(/\b\w/g, (char) => char?.toUpperCase());

export const validateTableFields = (dataArray, dispatch) => {
  for (let i = 0; i < dataArray.length; i++) {
    const obj = dataArray[i];
    for (const key in obj) {
      const value = obj[key];
      const isEmptyString = value === '';
      const isEmptyArray = Array.isArray(value) && value.length === 0;

      if (isEmptyString || isEmptyArray) {
        dispatch(notification(false, `${prettifyKey(key)} is required.`, true));
        return false;
      }
    }
  }
  return true;
};

export function validateMobileNumbers(numbers) {
  const invalidNumbers = numbers.filter((num) => !/^\d{8}$/.test(num));
  if (invalidNumbers.length > 0) {
    throw new Error(`ITS IDs must be exactly 8 digits`);
  }

  const duplicates = numbers.filter((num, i, arr) => arr.indexOf(num) !== i);
  if (duplicates.length > 0) {
    throw new Error(`Duplicate ITS IDs found`);
  }

  return numbers; // All numbers are valid and unique
}

export const getSingleData = (data, key, id) => {
  return data?.find((item) => item?.[key] === id);
};

export const createKGPayload = (data, { arazCityData = {}, kgData, zoneData, allDepartments, functionsData, arazCity, miqaatId }) => {
  const {
    LDName,
    email,
    phone,
    whatsapp,
    consentRequired,
    ITSID,
    hierarchyPosition,
    kgType,
    jamiat,
    jamaat,
    kgGroup,
    zone,
    age,
    gender,
    maritialStatus,
    prefix,
    misaq,
    occupation,
    qualification,
    idara,
    category,
    organization,
    address,
    city,
    country,
    nationality,
    vatan,
    logo,
    name,
    _id,
    consentAccepted,
    department,
    isInternationalPlugin,
    isExists,
    cityRoleID,
    function: functionId,
    otherFunction
  } = data || {};

  const { allKGType, allKGGroup, allHierarchyPosition } = kgData || {};
  const { zoneForSingleArazCity } = zoneData || {};
  const { departmentsforArazCity, departments } = allDepartments || {};

  const KGTypeDetail = kgType?.id ? getSingleData(allKGType, '_id', kgType?.id) : '';
  const KGGroupDetail = kgGroup?.id ? getSingleData(allKGGroup, '_id', kgGroup?.id) : '';
  const departmentDetail = department?.id
    ? getSingleData(departmentsforArazCity || departments, isEmpty(departmentsforArazCity) ? '_id' : 'id', department?.id)
    : '';
  const ZoneDetail = zone?.id ? getSingleData(zoneForSingleArazCity, '_id', zone?.id) : '';
  const hierarchyPositionDetail = hierarchyPosition?.id ? getSingleData(allHierarchyPosition, '_id', hierarchyPosition?.id) : '';

  return {
    name,
    LDName,
    ITSID,
    id: _id || null,
    email,
    phone,
    logo,
    whatsapp,
    cityRoleID,
    isExists: isExists || false,
    status: 'active',
    consentRequired: consentAccepted ? false : consentRequired || false,
    hierarchyPositionID: hierarchyPosition?.id || '',
    kgGroupID: kgGroup?.id || '',
    kgTypeID: kgType?.id || '',
    jamiatID: jamiat?.id || '',
    age,
    gender,
    maritialStatus,
    prefix,
    misaq,
    occupation,
    qualification,
    functionID: !CheckPositionRoleForConsent(hierarchyPosition?.id) ? functionId?.id || '' : '',
    otherFunction: otherFunction || '',
    idara,
    category,
    organization,
    address,
    city,
    country,
    nationality,
    vatan,
    jamaatID: jamaat?.id || '',
    departmentID: department?.id || '',
    isInternationalPlugin: isInternationalPlugin || false,
    arazCityID: arazCity || '',
    miqaatID: miqaatId || '',
    arazCityZoneID: zone?.id || '',
    consentAccepted: consentRequired ? false : true,
    // Additional Display Fields (optional)
    kgType: KGTypeDetail?.name || '',
    kgTypeColor: KGTypeDetail?.color || '',
    kgGroup: KGGroupDetail?.name || '',
    departmentName: !CheckPositionRoleForConsent(hierarchyPosition?.id) ? departmentDetail?.name : '',
    arazCityZoneName: !CheckPositionRoleForConsent(hierarchyPosition?.id) ? ZoneDetail?.name : '',
    hierarchyPositionName:
      !CheckPositionRoleForConsent(hierarchyPosition?.id) && arazCityData?.showPositionAlias
        ? hierarchyPositionDetail?.alias || ''
        : hierarchyPositionDetail?.name || ''
  };
};

export const createAssignKGPayload = ({
  data,
  kgData = {},
  arazCityData = {},
  zoneData = {},
  allDepartments = {},
  cityId = '',
  miqaatId = '',
  arazCityID = ''
}) => {
  const {
    LDName,
    email,
    phone,
    whatsapp,
    consentRequired,
    ITSID,
    hierarchyPosition,
    kgType,
    jamiatID,
    jamaatID,
    kgGroup,
    zone,
    age,
    gender,
    maritialStatus,
    prefix,
    misaq,
    occupation,
    qualification,
    idara,
    category,
    organization,
    address,
    city,
    country,
    nationality,
    vatan,
    logo,
    name,
    _id,
    cityRoleID,
    department,
    isInternationalPlugin,
    isExists,
    function: func,
    consentAccepted,
    otherFunction
  } = data || {};

  const { allKGType, allKGGroup, allHierarchyPosition } = kgData || {};
  const { zoneForSingleArazCity } = zoneData || {};
  const { departmentsforArazCity } = allDepartments || {};

  const getNameById = (list, id, idKey = '_id') => list?.find((item) => item?.[idKey] === id) || {};

  const KGTypeDetail = getNameById(allKGType, kgType);
  const KGGroupDetail = getNameById(allKGGroup, kgGroup);
  const departmentDetail = getNameById(departmentsforArazCity, department, 'id');
  const ZoneDetail = getNameById(zoneForSingleArazCity, zone);
  const hierarchyPositionDetail = getNameById(allHierarchyPosition, hierarchyPosition);
  return {
    name,
    LDName,
    ITSID,
    id: _id || null,
    email,
    phone,
    logo,
    whatsapp,
    cityRoleID,
    isExists: isExists || false,
    status: 'active',
    consentRequired: consentAccepted ? false : consentRequired || false,
    hierarchyPositionID: hierarchyPosition || '',
    kgGroupID: kgGroup || '',
    kgTypeID: kgType || '',
    jamiatID: jamiatID?._id || '',
    age,
    gender,
    maritialStatus,
    prefix,
    misaq,
    occupation,
    qualification,
    functionID: func || '',
    otherFunction: otherFunction || '',
    idara,
    category,
    organization,
    address,
    city,
    country,
    nationality,
    vatan,
    jamaatID: jamaatID?._id || '',
    departmentID: !CheckPositionRoleForConsent(hierarchyPosition) && department ? department : '',
    isInternationalPlugin: isInternationalPlugin || false,
    arazCityID: arazCityID || '',
    miqaatID: miqaatId || '',
    arazCityZoneID: zone || '', // Always include zone regardless of position
    consentAccepted: consentRequired ? false : true,
    kgType: KGTypeDetail?.name || '',
    kgTypeColor: KGTypeDetail?.color || '',
    kgGroup: KGGroupDetail?.name || '',
    departmentName: !CheckPositionRoleForConsent(hierarchyPosition) && department ? departmentDetail?.name : '',
    arazCityZoneName: zone ? ZoneDetail?.name : '', // Always show zone name regardless of position
    hierarchyPositionName: hierarchyPosition
      ? arazCityData?.showPositionAlias
        ? hierarchyPositionDetail?.alias || ''
        : hierarchyPositionDetail?.name || ''
      : ''
  };
};

export function sortByName(data) {
  return [...(data || [])].sort((a, b) => a.name.toLowerCase().localeCompare(b.name.toLowerCase()));
}

export function sortByLabel(data) {
  return [...(data || [])].sort((a, b) => {
    const labelA = typeof a?.label === 'string' ? a.label.toLowerCase() : '';
    const labelB = typeof b?.label === 'string' ? b.label.toLowerCase() : '';

    if (labelA === 'all') return -1;
    if (labelB === 'all') return 1;

    return labelA.localeCompare(labelB);
  });
}

export const extractTimeParts = (dateString) => {
  const localDate = new Date(dateString);

  let hours = localDate?.getHours();
  const minutes = localDate?.getMinutes();
  const period = hours >= 12 ? 'PM' : 'AM';

  hours = hours % 12;
  hours = hours === 0 ? 12 : hours;

  return {
    hh: hours || 0,
    mm: minutes || 0,
    period: period || ''
  };
};

export const getTime = (input = {}) => {
  const { hh, mm, period } = input;

  if (hh >= 0 && mm >= 0 && period) {
    const now = new Date();
    let hours = hh;

    if (period === 'PM' && hh !== 12) {
      hours += 12;
    } else if (period === 'AM' && hh === 12) {
      hours = 0;
    }

    return new Date(now.getFullYear(), now.getMonth(), now.getDate(), hours, mm, 0, 0);
  } else return '';
};

export const isQasreAliFemale = (ITSID, gender) => {
  if (ITSID?.startsWith('786') && gender === 'F') {
    return true;
  } else {
    return false;
  }
};

export const placeholderImg = (nameFirstLetter) => {
  if (nameFirstLetter?.length > 0) {
    return `https://ui-avatars.com/api/?name=${nameFirstLetter?.charAt(0).toUpperCase()}&background=f2dd60&color=fff&size=40&rounded=true`;
  } else {
    return `https://ui-avatars.com/api/?name=${H.charAt(0).toUpperCase()}&background=f2dd60&color=fff&size=40&rounded=true`;
  }
};

export const showZoneExceptCMZ = (zoneData) => {
  return getOptions(zoneData?.zoneForSingleArazCity, 'name')?.filter((item) => item?.label !== 'CMZ');
};

export const checkShowDepForKGReq = (departmentData, id) => {
  return departmentData?.some((item) => item?.id === id) ? id : '';
};

export const formatTimeOnly = (timestamp) => {
  if (!timestamp) return '-';

  const date = new Date(Number(timestamp));
  if (isNaN(date?.getTime())) return '-';

  return date?.toLocaleTimeString([], {
    hour: '2-digit',
    minute: '2-digit'
  });
};

export const getTodayTimestampFromTime = (originalTimestamp) => {
  const sourceDate = new Date(Number(originalTimestamp));
  const hours = sourceDate.getHours();
  const minutes = sourceDate.getMinutes();

  const today = new Date();
  return new Date(today.getFullYear(), today.getMonth(), today.getDate(), hours, minutes, 0, 0).getTime();
};
export const parseUniqueITSIDs = (input) => {
  const uniqueSet = new Set();

  (input || '')
    .split(/[\r\n, ]+/) // split by newlines, commas, or spaces
    .map((id) => id.trim())
    .forEach((id) => {
      if (id) uniqueSet.add(id);
    });

  return Array.from(uniqueSet);
};

export const getInvalidITSIDs = (ITSIDS) => {
  if (!ITSIDS) return true;

  const itsIDs =
    ITSIDS?.split('\n')
      ?.map((id) => id?.trim())
      ?.filter(Boolean) || [];

  const hasInvalid = itsIDs?.some((id) => !ID_8_DIGITS_REGEX?.test(id));

  return hasInvalid;
};
