// src/constants/apiConfig.js

import { BASE_URL } from 'config';
import { NODE_ENV } from 'config';

const isProduction = NODE_ENV === 'production' || import.meta.env.MODE === 'production';
// const isProduction = true;
// console.log('NODE_ENV from config:', NODE_ENV);
// console.log('Vite MODE:', import.meta.env.MODE);

// Define your development base URLs for each microservice
const developmentUrls = {
  gateway: 'http://localhost:8000/api',
  global: 'http://localhost:8001/api',
  hierarchy: 'http://localhost:8002/api',
  communication: 'http://localhost:8003/api',
  zonesCapacity: 'http://localhost:8004/api',
  taskManagement: 'http://localhost:8005/api',
  survey: 'http://localhost:8006/api',
  auth: 'http://localhost:8007/api'
};

/**
 * This new function builds the correct, full API URL.
 * @param {string} service - The name of the microservice (e.g., 'hierarchy', 'global').
 * @param {string} endpoint - The specific API endpoint (e.g., 'kg-user/its-one-login').
 * @returns {string} The full URL for the request.
 */
export const getApiUrl = (service, endpoint) => {
  if (isProduction) {
    // In production, all requests go to the single production URL.
    return `${BASE_URL}/${endpoint}`;
  }
  // In development, find the correct microservice URL from our list.
  const baseUrl = developmentUrls[service];
  if (!baseUrl) {
    throw new Error(`Service "${service}" not found in development URLs.`);
  }
  return `${baseUrl}/${endpoint}`;
};
