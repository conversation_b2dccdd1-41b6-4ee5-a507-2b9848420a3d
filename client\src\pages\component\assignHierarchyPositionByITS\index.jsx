import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Grid } from '@mui/material';
import Loader from 'components/Loader';
import { cloneDeep, get, isEmpty } from 'lodash';
import CustomForm from 'pages/component/form';
import FormButtons from 'pages/component/form/formButton';
import TableComponent from 'pages/component/table/table';
import ActionButtons from 'pages/component/table/tableComponent/actionButtons';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { assignPositionAction } from 'redux/actions/assignRoleAction';
import { getActiveDepartmentByArazCityAction } from 'redux/actions/departmentsAction';
import { getFunctionsByDepartmentAction } from 'redux/actions/functionAction';
import { getAllHierarchyPositions } from 'redux/actions/hierarchyPositionAction';
import { getAllKGGroupAction } from 'redux/actions/kgGroupAction';
import { getAllKGTypeAction } from 'redux/actions/kgtypeAction';
import { getMiqaatsAction } from 'redux/actions/miqaatActions';
import { getSystemRolesAction } from 'redux/actions/systemRoleAction';
import { getZoneyByArazCityAction } from 'redux/actions/zoneAction';
import { getUserDetail } from 'utils/auth';
import { createAssignKGPayload, getOptions, notification, updateInputFields } from 'utils/helper';
import { useAnalytics } from 'utils/userAnalytics';
import CustomModal from '../customModal';
import ConfirmationModal from '../customModal/confirmationModal';
import PageTitle from '../pageTitle';
import SelectMiqaatAndArazCity from '../SelectMiqaatAndArazCity';
import AlreadyAssignedUsers from './alreadyAssign';
import ImportResults from './assignUserResult';
import {
  AssignRoleByITSIDDefaultValues,
  AssignRoleByITSIDFields,
  AssignRoleByITSIDSchema,
  defaultHeaderValues,
  formatResponseWithMiqaatDetails,
  handleSingleKGSubmit,
  importITSIDs,
  updateAllRows,
  updateTableData,
  validateITSData
} from './constant';
import ImportITS from './importingITS';
import UserAlreadyExistsIndicator from './userAlreadyExistIndicator';

const AssignHierarchyPositionByITS = () => {
  const loggedInUser = getUserDetail();
  const kgData = useSelector((state) => state.kgData);
  const allArazCity = useSelector((state) => state.arazCity);
  const systemData = useSelector((state) => state.system);
  const miqaatData = useSelector((state) => state.miqaats);
  const zoneData = useSelector((state) => state.zone);
  const departmentData = useSelector((state) => state.departments);
  const functionsData = useSelector((state) => state.functions);
  const [modifiedField, setModifiedField] = useState(AssignRoleByITSIDFields);
  const [allITSID, setAllITSID] = useState([]);
  const [headerValues, setHeaderValues] = useState(defaultHeaderValues);
  const [steps, setSteps] = useState(1);
  const [alreadyExists, setAlreadyExists] = useState(0);
  const [deleteRow, setDeleteRow] = useState({});
  const [progress, setProgress] = useState({
    error: 0,
    success: 0,
    uploaded: 0
  });
  const [openModal, setOpenModal] = useState(false);
  const [results, setResults] = useState([]);
  const dispatch = useDispatch();
  const arazCityData = useSelector((state) => state.arazCity);
  const { trackUserActivity } = useAnalytics();

  const {
    control,
    handleSubmit,
    getValues,
    reset,
    formState: { errors }
  } = useForm({
    resolver: yupResolver(AssignRoleByITSIDSchema),
    defaultValues: AssignRoleByITSIDDefaultValues
  });

  useEffect(() => {
    dispatch(getActiveDepartmentByArazCityAction(getValues()?.arazCity));
  }, [getValues()?.arazCity]);

  useEffect(() => {
    dispatch(getAllHierarchyPositions());
    dispatch(getAllKGGroupAction());
    dispatch(getAllKGTypeAction());
    dispatch(getMiqaatsAction());
    dispatch(getSystemRolesAction());
    trackUserActivity({
      action: 'page_visit',
      page: 'Assign Hierarchy Position By ITS IDs',
      module: 'HR & Hierarchy'
    });
  }, []);

  useEffect(() => {
    let updatedFields = cloneDeep(AssignRoleByITSIDFields);
    let { miqaats } = miqaatData || {};

    if (!isEmpty(miqaats)) {
      updatedFields = updateInputFields(updatedFields, miqaats, 'miqaat', 'name');
    }
    setModifiedField(updatedFields);
  }, [miqaatData?.miqaats]);

  const onSelectChange = async (updatedValues) => {
    let currentValues = getValues();
    if (updatedValues?.arazCity) {
      dispatch(getZoneyByArazCityAction(updatedValues?.arazCity));
    }
    if (updatedValues?.miqaat) {
      reset({
        ...currentValues,
        ...updatedValues
      });
    } else {
      reset({
        ...currentValues,
        ...updatedValues,
        arazCity: ''
      });
    }
  };

  const onSubmit = async (isConfirm) => {
    const data = getValues();
    if (steps === 1) {
      if (data?.miqaat && data?.arazCity) {
        const itsIDs = data?.itsIDs
          ?.split('\n')
          .map((id) => id.trim())
          .filter((id) => id !== '');

        if (!itsIDs?.length) {
          dispatch(notification(false, 'ITS ID is required', true));
          return;
        }
        setOpenModal(true);
        const response = await importITSIDs(itsIDs, dispatch, setProgress, progress);
        if (response) {
          if (response?.length === itsIDs?.length) {
            if (steps === 1) {
              setTimeout(() => {
                setSteps((prevSteps) => prevSteps + 1);
                setOpenModal(false);
                setProgress({});
              }, 500);
            }
          }
          let updatedResponse = await formatResponseWithMiqaatDetails(response, {
            dispatch,
            miqaat: getValues()?.miqaat,
            kgData,
            departmentData,
            arazCity: getValues()?.arazCity
          });

          setAllITSID(updatedResponse || []);
          let isAlreadyExists = updatedResponse?.filter((item) => item?.isAlreadyExists === true)?.length;
          if (isAlreadyExists > 0) {
            setAlreadyExists(isAlreadyExists);
          } else {
            setAlreadyExists(0);
          }
        }
      } else {
        if (!data?.arazCity) {
          dispatch(notification(false, 'Please select araz city', true));
        }
        if (!data?.miqaat) {
          dispatch(notification(false, 'Please select miqaat ', true));
        }
      }
    } else if (steps === 2) {
      const isValid = validateITSData(allITSID, dispatch, kgData);

      if (!isValid) return;
      const payload = {
        arazCityID: data?.arazCity,
        miqaatID: data?.miqaat,
        existsInDB: allITSID
          ?.filter((item) => !item?.error)
          ?.map((item) => ({
            ...createAssignKGPayload({
              arazCityData,
              data: item,
              kgData,
              zoneData,
              functionsData,
              allDepartments: departmentData,
              miqaatId: data?.miqaat,
              arazCityID: data?.arazCity
            })
          })),
        notExistsInDB: []
      };
      if (payload?.existsInDB?.length === 0) {
        dispatch(notification(false, 'No User Imported', true));
      } else {
        let response = await dispatch(assignPositionAction(payload));
        if (get(response, 'payload.success', false)) {
          setSteps(1);
          setAlreadyExists(0);
          setResults(response?.payload?.data || []);
          reset({ ...AssignRoleByITSIDDefaultValues });
          setAllITSID([]);
        }
      }
    }
  };

  const handleCancel = () => {
    setAllITSID([]);
    setHeaderValues(defaultHeaderValues);
    if (steps > 1) {
      setSteps(steps - 1);
    }
  };

  const handleTableInput = async (rowData, value, name) => {
    let updatedITSData = await updateTableData(allITSID, rowData, name, value, kgData, departmentData?.departmentsforArazCity, dispatch);
    setAllITSID(updatedITSData);
  };

  const handleChangeAllRows = async (name, value, singleRowName) => {
    updateAllRows(name, value, singleRowName, {
      kgData,
      departmentData,
      dispatch,
      getFunctionsByDepartmentAction,
      setHeaderValues,
      setAllITSID,
      allITSID,
      headerValues,
      getOptions
    });
  };

  const headers = [
    {
      name: 'logo',
      type: 'image',
      title: '',
      sortingactive: true
    },
    {
      name: 'ITSID',
      type: 'text',
      title: 'ITS ID',
      sortingactive: true,
      width: '100px'
    },
    {
      name: 'name',
      type: 'text',
      title: 'Name',
      sortingactive: true
    },
    {
      name: 'jamiatID.name',
      type: 'text',
      title: 'Jamiat',
      sortingactive: true
    },
    {
      name: 'jamaatID.name',
      type: 'text',
      title: 'Jamaat',
      sortingactive: true
    },
    {
      name: 'errorMessage',
      type: 'info',
      title: 'Error',
      sortingactive: true
    },
    {
      name: 'actions',
      type: 'input',
      title: 'Int. plug-in',
      sortingactive: false,
      component: ActionButtons,
      minWidth: '150px',
      titleType: 'checkbox',
      inputName: 'allInternationalPlugin',
      onChange: (e) => {
        handleChangeAllRows('allInternationalPlugin', e.target.checked, 'isInternationalPlugin');
      },
      multipleButtons: [
        {
          type: 'checkbox',
          value: '',
          name: 'isInternationalPlugin',
          showButton: 'isUserFound',
          handleInputChange: async (type, name, value, rowData) => {
            await handleTableInput(rowData, value, name);
          }
        }
      ]
    },
    {
      name: 'actions',
      type: 'input',
      title: 'Consent',
      sortingactive: false,
      component: ActionButtons,
      minWidth: '100px',
      titleType: 'checkbox',
      inputName: 'allConsentRequired',
      onChange: (e) => {
        handleChangeAllRows('allConsentRequired', e?.target?.checked, 'consentRequired');
      },
      multipleButtons: [
        {
          type: 'checkbox',
          value: '',
          name: 'consentRequired',
          showButton: 'isUserFound',
          handleInputChange: async (type, name, value, rowData) => {
            await handleTableInput(rowData, value, name);
          }
        }
      ]
    },
    {
      name: 'actions',
      type: 'input',
      title: '',
      sortingactive: false,
      component: ActionButtons,
      minWidth: '410px',
      titleType: 'multipleInput',
      className: 'assign-kg-select-field',
      multipleHeaderInput: [
        {
          type: 'select',
          placeholder: 'KG Group',
          name: 'allKGGroup',

          className: 'assign-kg-select-field',
          size: 'small',
          items: getOptions(kgData?.allKGGroup, 'name'),
          onChange: (type, value, name) => {
            handleChangeAllRows('allKGGroup', value, 'kgGroup');
          }
        },
        {
          type: 'select',
          placeholder: 'KG Type',
          name: 'allKGType',

          className: 'assign-kg-select-field',
          size: 'small',
          items: getOptions(kgData?.allKGType, 'name'),
          onChange: (type, value, name) => {
            handleChangeAllRows('allKGType', value, 'kgType');
          }
        },
        {
          type: 'select',
          placeholder: 'Position',
          name: 'allPosition',
          className: 'assign-kg-select-field',
          size: 'small',
          items: getOptions(kgData?.allHierarchyPosition, arazCityData.showPositionAlias ? 'alias' : 'name'),
          onChange: (type, value, name) => {
            handleChangeAllRows('allPosition', value, 'hierarchyPosition');
          }
        },
        {
          type: 'select',
          placeholder: 'Department',
          name: 'allDepartment',
          // showButton: 'isDepartmental',
          className: 'assign-kg-select-field',
          size: 'small',
          items: getOptions(departmentData?.departmentsforArazCity, 'name', 'id'),
          onChange: (type, value, name) => {
            handleChangeAllRows('allDepartment', value, 'department');
          }
        },
        {
          type: 'select',
          placeholder: 'Function',
          name: 'allFunction',
          // showButton: 'isDepartmental',
          className: 'assign-kg-select-field',
          size: 'small',
          dynamicOptions: 'functionsData',
          items: getOptions(functionsData?.functionsForDepartment, 'name'),
          onChange: (type, value, name) => {
            handleChangeAllRows('allFunction', value, 'function');
          }
        },
        {
          type: 'text',
          placeholder: 'Other',
          name: 'otherFunction',
          // showButton: 'isOtherFunction',
          className: 'assign-kg-select-field',
          size: 'small',
          onChange: (type, value, name) => {
            handleChangeAllRows('otherFunction', value, 'otherFunction');
          }
        },
        {
          type: 'select',
          placeholder: 'Permission',
          name: 'allPermission',
          className: 'assign-kg-select-field',
          size: 'small',
          items: getOptions(systemData?.systemRoles, 'name'),
          onChange: (type, value, name) => {
            handleChangeAllRows('allPermission', value, 'cityRoleID');
          }
        },
        {
          type: 'select',
          placeholder: 'Zone',
          name: 'allZone',
          // showButton: 'isZonal',
          className: 'assign-kg-select-field',
          size: 'small',
          items: getOptions(zoneData?.zoneForSingleArazCity, 'name'),
          onChange: (type, value, name) => {
            handleChangeAllRows('allZone', value, 'zone');
          }
        }
      ],
      multipleButtons: [
        {
          type: 'select',
          placeholder: 'KG Group',
          name: 'kgGroup',
          showButton: 'isUserFound',
          className: 'assign-kg-select-field',
          size: 'small',
          items: getOptions(kgData?.allKGGroup, 'name'),
          handleInputChange: async (type, name, value, rowData) => {
            await handleTableInput(rowData, value, name);
          }
        },
        {
          type: 'select',
          placeholder: 'KG Type',
          name: 'kgType',
          showButton: 'isUserFound',
          className: 'assign-kg-select-field',
          size: 'small',
          items: getOptions(kgData?.allKGType, 'name'),
          handleInputChange: async (type, name, value, rowData) => {
            await handleTableInput(rowData, value, name);
          }
        },
        {
          type: 'select',
          placeholder: 'Position',
          value: '',
          showButton: 'isUserFound',
          className: 'assign-kg-select-field',
          size: 'small',
          name: 'hierarchyPosition',
          items: getOptions(kgData?.allHierarchyPosition, arazCityData.showPositionAlias ? 'alias' : 'name'),
          handleInputChange: async (type, name, value, rowData) => {
            await handleTableInput(rowData, value, name);
          }
        },
        {
          type: 'select',
          placeholder: 'Department',
          value: '',
          className: 'assign-kg-select-field',
          size: 'small',
          showButton: 'isDepartmental',
          name: 'department',
          items: getOptions(departmentData?.departmentsforArazCity, 'name', 'id'),
          handleInputChange: (type, name, value, rowData) => {
            handleTableInput(rowData, value, name);
          }
        },
        {
          type: 'select',
          placeholder: 'Function',
          value: '',
          className: 'assign-kg-select-field',
          size: 'small',
          showButton: 'isDepartmental',
          name: 'function',
          itemName: 'functionsData',
          items: [],
          handleInputChange: async (type, name, value, rowData) => {
            await handleTableInput(rowData, value, name);
          }
        },
        {
          type: 'text',
          placeholder: 'Other',
          name: 'otherFunction',
          showButton: 'isOtherFunction',
          className: 'assign-kg-select-field',
          size: 'small',
          handleInputChange: async (type, name, value, rowData) => {
            await handleTableInput(rowData, value, name);
          }
        },
        {
          type: 'select',
          placeholder: 'System Permission',
          value: '',
          className: 'assign-kg-select-field',
          size: 'small',
          showButton: 'isUserFound',
          name: 'cityRoleID',
          items: getOptions(systemData?.systemRoles, 'name'),
          handleInputChange: (type, name, value, rowData) => {
            handleTableInput(rowData, value, name);
          }
        },
        {
          type: 'select',
          placeholder: 'Zone',
          value: '',
          showButton: 'isZonal',
          className: 'assign-kg-select-field',
          size: 'small',
          name: 'zone',
          items: getOptions(zoneData?.zoneForSingleArazCity, 'name'),
          handleInputChange: async (type, name, value, rowData) => {
            await handleTableInput(rowData, value, name);
          }
        }
      ]
    },
    {
      name: 'actions',
      type: 'actions',
      title: 'Actions',
      sortingactive: false,
      component: ActionButtons,
      multipleButtons: [
        {
          type: 'button',
          showButton: 'isUserFound',
          buttonTitle: 'Save',
          action: 'save',
          buttonOnClick: async (type, rowData) => {
            await handleSingleKGSubmit({
              arazCityData,
              data: rowData,
              miqaatID: getValues()?.miqaat,
              arazCityID: getValues()?.arazCity,
              departmentData,
              zoneData,
              functionsData,
              dispatch,
              allITSID,
              handleCancel,
              kgData,
              setAllITSID
            });
          },
          color: 'primary',
          tooltip: 'Save'
        },
        {
          type: 'button',
          showButton: 'isUserFound',
          buttonTitle: 'Delete',
          action: 'save',
          buttonOnClick: async (type, rowData) => {
            setDeleteRow({ ...rowData });
          },
          color: 'error',
          tooltip: 'Delete'
        }
      ]
    }
  ];

  const isLoading = () => {
    return functionsData?.loading || kgData?.loading;
  };

  const handleCloseDeleteModal = () => {
    setDeleteRow({});
  };

  const handleDeleteRow = () => {
    let updatedRow = allITSID?.filter((item) => item?.ITSID !== deleteRow?.ITSID);
    setAllITSID(updatedRow);
    setDeleteRow({});
  };

  const handleCloseExistModal = () => {
    setAlreadyExists(0);
  };

  const handleRemoveExistModal = () => {
    setAlreadyExists(0);
    let updatedRow = allITSID?.filter((item) => item?.isAlreadyExists !== true);
    setAllITSID(updatedRow);
  };

  const DeleteModalButtons = [
    { label: 'Cancel', onClick: () => handleCloseDeleteModal(), color: 'error', disabled: isLoading() },
    { label: 'Confirm', onClick: () => handleDeleteRow(), variant: 'contained', color: 'primary', disabled: isLoading() }
  ];

  const alreadyExistsModalButtons = [
    { label: 'Override', onClick: () => handleCloseExistModal(), color: 'error', disabled: isLoading() },
    { label: 'Proceed', onClick: () => handleRemoveExistModal(), variant: 'contained', color: 'primary', disabled: isLoading() }
  ];

  const getTotalFiles = () => {
    return getValues()
      ?.itsIDs?.split('\n')
      .map((id) => id.trim())
      .filter((id) => id !== '').length;
  };

  const handleCloseResults = () => {
    setResults([]);
    setHeaderValues(defaultHeaderValues);
  };

  return (
    <>
      <CustomModal
        borderRadius="20px"
        Component={<ImportITS progress={progress} total={getTotalFiles()} loading={isLoading()} />}
        open={openModal}
      />
      {alreadyExists > 0 && (
        <CustomModal
          borderRadius="20px"
          buttons={alreadyExistsModalButtons}
          Component={
            <>
              <AlreadyAssignedUsers userCount={alreadyExists} />
            </>
          }
          open={alreadyExists > 0}
        />
      )}

      {!isEmpty(deleteRow) && (
        <CustomModal
          cancel={handleCloseDeleteModal}
          buttons={DeleteModalButtons}
          borderRadius="20px"
          Component={<ConfirmationModal title="Row" />}
          open={true}
        />
      )}

      {!isEmpty(results) && (
        <CustomModal
          cancel={handleCloseResults}
          // fullScreen={true}
          borderRadius="20px"
          Component={<ImportResults results={results} handleCloseResults={handleCloseResults} />}
          open={true}
        />
      )}

      {steps === 1 ? (
        <CustomForm
          loading={kgData?.loading}
          title={'Assign Hierarchy Position (By ITS IDs)'}
          fieldValues={getValues()}
          fields={[
            {
              type: 'component',
              Component: SelectMiqaatAndArazCity,
              componentProps: {
                hideSearchField: true,
                values: getValues(),
                xl: 6,
                md: 6,
                xs: 12,
                errors: errors,
                onSelectChange: (type, name, value) => onSelectChange(type, name, value)
              }
            },
            ...modifiedField
          ]}
          control={control}
          errors={errors}
          handleInputChange={(type, name, value) => {
            if (name === 'itsIDs') reset({ ...getValues(), [name]: value });
          }}
          inputFieldWrapper="assign-kg-form-fields"
          handleSubmit={handleSubmit(onSubmit)}
          submitButtonLabel={steps === 2 ? 'Submit' : 'Next'}
          hideCancelButton={true}
        />
      ) : (
        <Box className="import-file-table-wrapper ">
          {isLoading() && <Loader />}
          <PageTitle title={'Assign Hierarchy Position (By ITS IDs)'} handleCancel={handleCancel} />
          <div className="form-main-card bulk-import">
            <TableComponent
              tableContainerClassName={'import-file-table sticky-table-container'}
              columns={headers}
              headerValues={headerValues}
              rows={allITSID}
              enablePagination={true}
              rowTooltip="errorMessage"
              headerClassName="sticky-table-head"
            />
            <UserAlreadyExistsIndicator />
            <Grid item xs={12} px={2} className="import-file-form-submit-button">
              <FormButtons handleSubmit={() => onSubmit()} submitButtonLabel={'Submit'} onCancel={handleCancel} loading={kgData?.loading} />
            </Grid>
          </div>
        </Box>
      )}
    </>
  );
};
export default AssignHierarchyPositionByITS;
