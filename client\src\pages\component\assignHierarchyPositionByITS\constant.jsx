import { chunk } from 'lodash';
import { getKGDetailByITSIDsAction } from 'redux/actions/assignRoleAction';
import { getFunctionsByDepartmentAction } from 'redux/actions/functionAction';
import { ID_8_DIGITS_REGEX } from 'utils/constant';
import { createAssignKGPayload, getOptions, notification } from 'utils/helper';
import * as yup from 'yup';
import { otherFunctionId } from '../kgUsers/constant';
import { validation } from '../assignHierarchyPositionFile/constant';
import { addKGAction } from 'redux/actions/kgAction';
export const AssignRoleByITSIDDefaultValues = {
  miqaat: '',
  arazCity: '',
  zone: '',
  itsIDs: ''
};

export const handleSingleKGSubmit = async ({
  kgData,
  data,
  arazCityData,
  dispatch,
  departmentData,
  zoneData,
  functionsData,
  setAllITSID,
  allITSID,
  miqaatID,
  arazCityID,
  handleCancel
}) => {
  let response = {};
  let payload = createAssignKGPayload({
    data,
    kgData,
    arazCityData,
    zoneData,
    allDepartments: departmentData,
    miqaatId: miqaatID,
    arazCityID
  });

  let isValid = validation(kgData, dispatch, payload);

  if (isValid) {
    response = await dispatch(addKGAction(payload));

    if (response?.payload?.success) {
      setAllITSID((prevITSIDs) => prevITSIDs.filter((item) => item.ITSID !== data?.ITSID));
      let updatedFields = allITSID?.filter((item) => item.ITSID !== data?.ITSID);

      if (updatedFields?.length === 0) {
        handleCancel();
      }
    }
  }
};

export const DEFAULT_KG_GROUP = '678915d672fa3ee5bb648dc1';
export const DEFAULT_KG_TYPE = '67cb50094db9413c913188bc';

export const defaultHeaderValues = {
  allConsentRequired: true,
  allKGGroup: DEFAULT_KG_GROUP,
  allKGType: DEFAULT_KG_TYPE
};

export const AssignRoleByITSIDSchema = yup.object().shape({
  itsIDs: yup
    .string()
    .required('ITS ID is required')
    .test('valid-itsIDs', 'Enter valid ITS IDs (one per line)', (value) => {
      if (!value) return false;
      let ids = value
        .split('\n')
        .map((id) => id.trim())
        .filter((id) => id !== '');
      return ids.length > 0;
    })
    .test('valid-format', 'ITS ID must be of 8 digits with no duplicates.', (value) => {
      if (!value) return false;
      let ids = value
        .split('\n')
        .map((id) => id.trim())
        .filter((id) => id !== '');
      let areAllDigits = ids.every((id) => ID_8_DIGITS_REGEX.test(id));
      let uniqueIds = new Set(ids);
      return areAllDigits && uniqueIds.size === ids.length;
    })
});

export const AssignRoleByITSIDFields = [
  {
    name: 'itsIDs',
    label: 'ITS IDs',
    type: 'textarea',
    className: 'text-area-description',
    acceptedFormat: '.csv,.xlsx,.xls',
    minRows: 8,
    md: 12,
    isNumeric: true,
    placeholder: 'Enter ITS IDs (one per line)'
  }
];

export const validateITSData = (fileData, dispatch, kgData) => {
  return fileData.every((item, index) => {
    if (!item?._id) return true;
    let missingFields = [];
    let hierarchyPosition = kgData?.allHierarchyPosition?.find((position) => position?._id === item?.hierarchyPosition);

    if (hierarchyPosition?.isZonal && !item?.zone) missingFields.push('Zone');
    if (hierarchyPosition?.isDepartmental && !item?.department) missingFields.push('Department');

    if (missingFields.length > 0) {
      dispatch(
        notification(false, `${missingFields.join(' , ')} in row ${index + 1} ${missingFields.length > 1 ? 'are' : 'is'} required`, true)
      );
      return false;
    }
    return true;
  });
};

export const updateTableData = async (allITSID, rowData, name, value, kgData, departmentData, dispatch) => {
  let functions = [];
  if (name === 'department') {
    if (value) {
      let response = await dispatch(getFunctionsByDepartmentAction(value));
      functions = response?.payload.data || [];
    } else {
      functions = [];
    }
  }

  return allITSID?.map((item) => {
    if (item?._id === rowData?._id) {
      if (name === 'hierarchyPosition') {
        let hierarchyPosition = kgData?.allHierarchyPosition?.find((position) => position?._id === value);

        return {
          ...item,
          [name]: value,
          department: '',
          function: '',
          zone: '',
          functionsData: [],
          cityRoleID: '',
          isOtherFunction: false,
          otherFunction: '',
          isZonal: true,
          isDepartmental: hierarchyPosition?.isDepartmental && item?.isUserFound
        };
      }

      if (name === 'department') {
        let singleDepartment = departmentData?.find((department) => department?.id === value);

        return {
          ...item,
          [name]: value,
          functionsData: getOptions(functions, 'name'),
          function: '',
          isOtherFunction: false,
          otherFunction: '',
          cityRoleID: singleDepartment?.cityRoleID
        };
      }

      if (name === 'function') {
        return {
          ...item,
          [name]: value,
          isOtherFunction: value === otherFunctionId
        };
      }

      return { ...item, [name]: value };
    }

    return item;
  });
};

export const importITSIDs = async (data, dispatch, setProgress, progress, action) => {
  const chunkSize = 5;
  const chunks = chunk(data, chunkSize);
  let allResponses = [];
  let successCount = 0;
  let errorCount = 0;
  let dispatchAction = action ? action : getKGDetailByITSIDsAction;
  for (let i = 0; i < chunks?.length; i++) {
    try {
      const response = await dispatch(dispatchAction({ ITS_IDs: chunks[i] }));

      if (!response?.payload?.data || response?.payload?.data?.length === 0) {
        chunks?.[i]?.forEach((ITSID, index) => {
          allResponses?.push({ ITSID, error: true, errorMessage: 'ITS User Not Found', id: response?.payload?.data[index]._id });
        });
        errorCount += chunks?.[i]?.length;
      } else {
        response?.payload?.data?.forEach((item) => {
          if (item?.error) {
            errorCount++;
          } else {
            successCount++;
          }
          allResponses?.push({ ...item, isUserFound: !item?.error });
        });
      }
    } catch (error) {
      console.log('Error fetching data for chunk:', error);
      chunks?.[i]?.forEach((ITSID) => {
        allResponses.push({ ITSID, error: true, errorMessage: 'API Error - ITS User Not Found' });
      });

      errorCount += chunks?.[i]?.length;
    }

    setProgress({ uploaded: allResponses?.length, success: successCount, error: errorCount });
  }

  return allResponses;
};

export const sortedRows = (rows) => {
  return rows.sort((a, b) => {
    if (a.error && !b.error) return 1; // Move error rows to the end
    if (!a.error && b.error) return -1;
    return 0; // Maintain order for non-error rows
  });
};

export const updateAllRows = async (name, value, singleRowName, dependencies) => {
  const { kgData, departmentData, dispatch, getFunctionsByDepartmentAction, setHeaderValues, setAllITSID, allITSID, headerValues } =
    dependencies;

  let updatedHeaderValues = { ...headerValues, [name]: value };
  let updatedRows = allITSID?.map((item) => (item?.error ? item : { ...item, [singleRowName]: value }));

  if (name === 'allPosition') {
    let hierarchyPosition = kgData?.allHierarchyPosition?.find((pos) => pos?._id === value);
    updatedHeaderValues = {
      ...updatedHeaderValues,
      isZonal: hierarchyPosition?.isZonal,
      isDepartmental: hierarchyPosition?.isDepartmental,
      isOtherFunction: false,
      allDepartment: '',
      functionsData: [],
      allZone: '',
      allFunction: '',
      otherFunction: '',
      allPermission: ''
    };
    updatedRows = updatedRows?.map((item) =>
      item?.error
        ? item
        : {
            ...item,
            isZonal: (hierarchyPosition?.isZonal && item?.isUserFound) || false,
            isDepartmental: (hierarchyPosition?.isDepartmental && item?.isUserFound) || false,
            zone: '',
            department: '',
            function: '',
            isOtherFunction: false,
            otherFunction: '',
            cityRoleID: ''
          }
    );
  } else if (name === 'allDepartment') {
    let response = await dispatch(getFunctionsByDepartmentAction(value));
    let singleDepartment = departmentData?.departmentsforArazCity?.find((dept) => dept?.id === value);
    updatedHeaderValues = {
      ...updatedHeaderValues,
      allPermission: singleDepartment?.cityRoleID,
      isOtherFunction: false,
      allFunction: '',
      otherFunction: '',
      functionsData: getOptions(response?.payload?.data || [], 'name')
    };
    updatedRows = updatedRows.map((item) =>
      item?.error
        ? item
        : {
            ...item,
            isOtherFunction: false,
            function: '',
            otherFunction: '',
            functionsData: getOptions(response?.payload?.data || [], 'name'),
            cityRoleID: singleDepartment?.cityRoleID
          }
    );
  } else if (name === 'allFunction') {
    updatedHeaderValues = {
      ...updatedHeaderValues,
      isOtherFunction: value === otherFunctionId,
      otherFunction: ''
    };
    updatedRows = updatedRows.map((item) =>
      item?.error
        ? item
        : {
            ...item,
            isOtherFunction: value === otherFunctionId && item?.isUserFound,
            otherFunction: ''
          }
    );
  }

  setHeaderValues(updatedHeaderValues);
  setAllITSID(updatedRows);
};

export const formatResponseWithMiqaatDetails = async (
  response = [],
  { dispatch, miqaat, arazCity, kgData, departmentData, arazCityData, changeColor }
) => {
  const updatedRes = await Promise.all(
    response.map(async (item) => {
      const miqaatID = miqaat;
      const userDetailInMiqaat = item?.miqaats?.find(
        (miqaat) => miqaat?.isActive && miqaat?.miqaatID === miqaatID && miqaat?.arazCityID === arazCity
      );

      let singleArazCity = arazCityData?.arazCityForSingleMiqaat?.find((city) => city?._id === userDetailInMiqaat?.arazCityID);

      const hierarchyPosition = kgData?.allHierarchyPosition?.find((pos) => pos?._id === userDetailInMiqaat?.hierarchyPositionID);
      let res = {};
      if (userDetailInMiqaat?.departmentID) {
        res = await dispatch(getFunctionsByDepartmentAction(userDetailInMiqaat?.departmentID));
      }
      const singleDepartment = departmentData?.departmentsforArazCity?.find((dept) => dept?.id === userDetailInMiqaat?.departmentID);
      return {
        ...item,
        ...userDetailInMiqaat,
        consentRequired: userDetailInMiqaat?.consentAccepted ? false : userDetailInMiqaat?.consentRequired || true,
        kgType: DEFAULT_KG_TYPE,
        kgGroup: DEFAULT_KG_GROUP,
        functionsData: getOptions(res?.payload?.data || [], 'name'),
        isZonal: hierarchyPosition?.isZonal,
        allPermission: singleDepartment?.cityRoleID,
        department: userDetailInMiqaat?.departmentID,
        zone: userDetailInMiqaat?.arazCityZoneID,
        cityRoleID: userDetailInMiqaat?.cityRoleID || singleDepartment?.cityRoleID,
        function: userDetailInMiqaat?.functionID,
        kgType: userDetailInMiqaat?.kgTypeID || DEFAULT_KG_TYPE,
        kgGroup: userDetailInMiqaat?.kgGroupID || DEFAULT_KG_GROUP,
        hierarchyPosition: userDetailInMiqaat?.hierarchyPositionID,
        isDepartmental: hierarchyPosition?.isDepartmental,
        isOtherFunction: userDetailInMiqaat?.functionID === otherFunctionId ? true : false,
        otherFunction: userDetailInMiqaat?.otherFunction,
        hierarchyPositionName: singleArazCity?.showPositionAlias
          ? userDetailInMiqaat?.hierarchyPositionAliasName
          : userDetailInMiqaat?.hierarchyPositionName,
        zoneName: userDetailInMiqaat?.zoneName,
        departmentName: userDetailInMiqaat?.departmentName,
        functionName: userDetailInMiqaat?.functionName,
        priorityOne: item?.interestOne,
        priorityTwo: item?.interestTwo,
        priorityThree: item?.interestThree,
        isAlreadyExists: !changeColor ? (userDetailInMiqaat?.hierarchyPositionID ? true : false) : ''
      };
    })
  );

  return updatedRes;
};
