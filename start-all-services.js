const { spawn, exec } = require("child_process");
const path = require("path");
const kill = require("tree-kill");
const os = require("os");
const { NODE_ENV } = require("./constants");

// Define all microservices
const services = [
  {
    name: "Gateway",
    script: "gateway.js",
    port: process.env.GATEWAY_PORT || 8000,
  },
  {
    name: "Global",
    script: "services/global/global.service.js",
    port: process.env.GLOBAL_PORT || 8001,
  },
  {
    name: "Hierarchy",
    script: "services/hierarchy.service.js",
    port: process.env.HIERARCHY_PORT || 8002,
  },
  {
    name: "Communication",
    script: "services/communication.service.js",
    port: process.env.COMMUNICATION_PORT || 8003,
  },
  {
    name: "Zones Capacity",
    script: "services/zonesCapacity.service.js",
    port: process.env.ZONES_CAPACITY_PORT || 8005,
  },
  {
    name: "Task Management",
    script: "services/taskmanagement.service.js",
    port: process.env.TASK_MANAGEMENT_PORT || 8006,
  },
  {
    name: "Survey",
    script: "services/survey.service.js",
    port: process.env.SURVEY_PORT || 8010,
  },
  {
    name: "Auth Service",
    script: "services/auth.service.js",
    port: process.env.AUTH_SERVICE_PORT || 8012,
  },
];

const processes = [];

// Ports used by your services (matching your .env file)
const {
  GATEWAY_PORT,
  GLOBAL_PORT,
  HIERARCHY_PORT,
  COMMUNICATION_PORT,
  ZONES_CAPACITY_PORT,
  TASK_MANAGEMENT_PORT,
  SURVEY_PORT,
  AUTH_SERVICE_PORT,
} = require("./constants");

// Ports used by your services
const PORTS = [
  GATEWAY_PORT,
  GLOBAL_PORT,
  HIERARCHY_PORT,
  COMMUNICATION_PORT,
  ZONES_CAPACITY_PORT,
  TASK_MANAGEMENT_PORT,
  SURVEY_PORT,
  AUTH_SERVICE_PORT,
];

// Kill processes on specific ports
const killProcessOnPort = (port) => {
  return new Promise((resolve) => {
    if (os.platform() === "win32") {
      // Windows
      exec(`netstat -ano | findstr :${port}`, (error, stdout) => {
        if (error || !stdout) {
          resolve();
          return;
        }

        const lines = stdout.trim().split("\n");
        const pids = new Set();

        lines.forEach((line) => {
          const parts = line.trim().split(/\s+/);
          if (parts.length >= 5) {
            const pid = parts[parts.length - 1];
            if (pid && pid !== "0") {
              pids.add(pid);
            }
          }
        });

        if (pids.size === 0) {
          resolve();
          return;
        }

        console.log(
          `   🔹 Port ${port}: Killing PIDs [${Array.from(pids).join(", ")}]`
        );

        let killedCount = 0;
        pids.forEach((pid) => {
          exec(`taskkill /F /PID ${pid}`, (killError) => {
            killedCount++;
            if (killedCount === pids.size) {
              resolve();
            }
          });
        });
      });
    } else {
      // Unix/Linux/Mac
      exec(`lsof -ti:${port}`, (error, stdout) => {
        if (error || !stdout) {
          resolve();
          return;
        }

        const pids = stdout
          .trim()
          .split("\n")
          .filter((pid) => pid);
        console.log(`   🔹 Port ${port}: Killing PIDs [${pids.join(", ")}]`);

        let killedCount = 0;
        pids.forEach((pid) => {
          exec(`kill -9 ${pid}`, (killError) => {
            killedCount++;
            if (killedCount === pids.length) {
              resolve();
            }
          });
        });
      });
    }
  });
};

// Integrated kill all services function
const killAllServices = async (shouldExit = true) => {
  console.log("\n🛑 Shutting down all AMS services...");

  // First, try to kill child processes gracefully
  processes.forEach(({ name, child }) => {
    if (child && !child.killed) {
      console.log(`   🔹 Stopping ${name}...`);
      if (child.pid) {
        kill(child.pid, "SIGTERM");
      }
    }
  });

  // Wait a moment for graceful shutdown
  await new Promise((resolve) => setTimeout(resolve, 2000));

  // Then kill any remaining processes on our ports
  console.log("🧹 Cleaning up any remaining processes on service ports...");
  for (const port of PORTS) {
    await killProcessOnPort(port);
  }

  console.log("✅ All services stopped successfully!");

  if (shouldExit) {
    process.exit(0);
  }
};

// Start one service
function startService(service) {
  console.log(`🔹 Starting ${service.name} on port ${service.port}...`);

  const cmd = NODE_ENV === "production" ? "node" : "npx";
  const args =
    NODE_ENV === "production"
      ? ["--max-old-space-size=2560", service.script]
      : ["nodemon", "--max-old-space-size=2560", service.script];

  const child = spawn(cmd, args, {
    stdio: "inherit",
    cwd: __dirname,
    shell: true, // Required on Windows for npx/nodemon
  });

  child.on("error", (err) => {
    console.error(`❌ Failed to start ${service.name}:`, err);
  });

  child.on("exit", (code) => {
    console.log(`⚠️ ${service.name} exited with code ${code}`);
    // Remove from processes array when service exits
    const index = processes.findIndex((p) => p.child.pid === child.pid);
    if (index > -1) {
      processes.splice(index, 1);
    }
  });

  // Store both name and child process reference
  processes.push({ name: service.name, child: child, pid: child.pid });
}

// Handle shutdown signals
process.on("SIGINT", () => {
  console.log("\n📴 Received SIGINT (Ctrl+C)...");
  killAllServices();
});

process.on("SIGTERM", () => {
  console.log("\n📴 Received SIGTERM...");
  killAllServices();
});

process.on("SIGHUP", () => {
  console.log("\n📴 Received SIGHUP...");
  killAllServices();
});

// Handle uncaught exceptions
process.on("uncaughtException", (error) => {
  console.error("❌ Uncaught Exception:", error);
  killAllServices();
});

process.on("unhandledRejection", (reason, promise) => {
  console.error("❌ Unhandled Rejection at:", promise, "reason:", reason);
  killAllServices();
});

// Async function to launch services with delay
async function startAll() {
  // Clean up any existing processes first
  console.log("🧹 Cleaning up any existing processes...");
  await killAllServices(false).catch(() => {}); // Don't exit on cleanup errors

  console.log("🚀 Starting AMS Microservices...\n");

  for (const service of services) {
    startService(service);
    await new Promise((res) => setTimeout(res, 1000)); // Delay between starts
  }

  console.log("\n✅ All services started. Press Ctrl+C to stop.\n");
  console.log("🌐 Service URLs:");
  services.forEach((service) => {
    console.log(`${service.name}: http://localhost:${service.port}`);
  });
  console.log("");
}

startAll();
