import { yupResolver } from '@hookform/resolvers/yup';
import { Box, Grid, Typography } from '@mui/material';
import Loader from 'components/Loader';
import { chunk, get, isEmpty } from 'lodash';
import CustomForm from 'pages/component/form';
import FormButtons from 'pages/component/form/formButton';
import TableComponent from 'pages/component/table/table';
import ActionButtons from 'pages/component/table/tableComponent/actionButtons';
import { useEffect, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useDispatch, useSelector } from 'react-redux';
import { assignKGUserAction, assignPositionAction } from 'redux/actions/assignRoleAction';
import { getDepartmentsAction } from 'redux/actions/departmentsAction';
import { getFunctionsByDepartmentAction } from 'redux/actions/functionAction';
import { getAllHierarchyPositions } from 'redux/actions/hierarchyPositionAction';
import { getAllKGGroupAction } from 'redux/actions/kgGroupAction';
import { getAllKGTypeAction } from 'redux/actions/kgtypeAction';
import { getMiqaatsAction } from 'redux/actions/miqaatActions';
import { getSystemRolesAction } from 'redux/actions/systemRoleAction';
import { getZoneyByArazCityAction } from 'redux/actions/zoneAction';
import { getUserDetail } from 'utils/auth';
import { createAssignKGPayload, notification } from 'utils/helper';
import { useAnalytics } from 'utils/userAnalytics';
import AlreadyAssignedUsers from '../assignHierarchyPositionByITS/alreadyAssign';
import ImportResults from '../assignHierarchyPositionByITS/assignUserResult';
import {
  defaultHeaderValues,
  formatResponseWithMiqaatDetails,
  handleSingleKGSubmit,
  importITSIDs,
  updateAllRows,
  updateTableData,
  validateITSData
} from '../assignHierarchyPositionByITS/constant';
import ImportITS from '../assignHierarchyPositionByITS/importingITS';
import UserAlreadyExistsIndicator from '../assignHierarchyPositionByITS/userAlreadyExistIndicator';
import CustomModal from '../customModal';
import ConfirmationModal from '../customModal/confirmationModal';
import PageTitle from '../pageTitle';
import SelectMiqaatAndArazCity from '../SelectMiqaatAndArazCity';
import { AssignRoleByFileDefaultValues, AssignRoleByFileFields, AssignRoleByFileSchema } from './constant';
import * as XLSX from 'xlsx';

const AssignHierarchyPositionFile = () => {
  const loggedInUser = getUserDetail();
  const kgData = useSelector((state) => state.kgData);
  const systemData = useSelector((state) => state.system);
  const zoneData = useSelector((state) => state.zone);
  const departmentData = useSelector((state) => state.departments);
  const functionsData = useSelector((state) => state.functions);
  const [results, setResults] = useState([]);
  const [fileData, setFileData] = useState([]);
  const [headerValues, setHeaderValues] = useState(defaultHeaderValues);
  const [alreadyExists, setAlreadyExists] = useState(0);
  const [deleteRow, setDeleteRow] = useState({});
  const [steps, setSteps] = useState(1);
  const [fileName, setFileName] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [progress, setProgress] = useState({
    error: 0,
    success: 0,
    uploaded: 0
  });
  const [openModal, setOpenModal] = useState(false);
  const arazCityData = useSelector((state) => state.arazCity);
  const dispatch = useDispatch();
  const {
    control,
    handleSubmit,
    getValues,
    reset,
    formState: { errors }
  } = useForm({
    // resolver: yupResolver(AssignRoleByFileSchema),
    defaultValues: AssignRoleByFileDefaultValues
  });
  const { trackUserActivity } = useAnalytics();

  useEffect(() => {
    dispatch(getAllHierarchyPositions());
    dispatch(getAllKGGroupAction());
    dispatch(getAllKGTypeAction());
    dispatch(getMiqaatsAction());
    dispatch(getDepartmentsAction());
    dispatch(getSystemRolesAction());
    trackUserActivity({
      action: 'page_visit',
      page: 'Assign Hierarchy Position (File Import)',
      module: 'HR & Hierarchy'
    });
  }, []);

  useEffect(() => {
    setFileData(kgData?.kgITSDetails?.map((item) => ({ ...item, isUserFound: !item?.error })) || []);
  }, [kgData?.kgITSDetails]);

  // useEffect(() => {
  //   let updatedFields = cloneDeep(AssignRoleByFileFields);
  //   let { miqaats } = miqaatData || {};

  //   if (!isEmpty(miqaats)) {
  //     updatedFields = updateInputFields(updatedFields, miqaats, 'miqaat', 'name');
  //   }
  //   setModifiedField(updatedFields);
  // }, [miqaatData?.miqaats]);

  const filterFileData = (file) => {
    return file
      .map((item) => {
        const filteredItem = Object.fromEntries(Object.entries(item).filter(([key, value]) => key !== ''));
        return Object.keys(filteredItem).length > 0 ? filteredItem : null;
      })
      .filter(Boolean)
      ?.map((item) => item?.ITS_ID);
  };

  const onSelectChange = async (updatedValues) => {
    let currentValues = getValues();
    if (updatedValues?.arazCity) {
      dispatch(getZoneyByArazCityAction(updatedValues?.arazCity));
    }
    if (updatedValues?.miqaat) {
      reset({
        ...currentValues,
        ...updatedValues
      });
    } else {
      reset({
        ...currentValues,
        ...updatedValues,
        arazCity: ''
      });
    }
  };

  const handleInputChange = async (type, name, value, fileName) => {
    console.log(value, 'value');
    switch (name) {
      case 'file':
        setFileName(fileName);
        break;
    }
  };

  const onSubmit = async (isConfirm) => {
    let data = getValues();
    let { file, arazCity, zone, miqaat, overwriteRecords, overwriteKGGroup, overwriteKGType, overwritePermission } = data;

    if (!miqaat || !arazCity) {
      dispatch(notification(false, 'Please select Miqaat and Araz City', true));
      return;
    }

    if (steps === 1) {
      const allUsers = (file ?? [])
        .filter((f) => f?.ITS_ID)
        .map((f) => {
          const { ITS_ID, ...rest } = f || {};
          return {
            ITSID: ITS_ID ? String(ITS_ID) : '',
            ...Object.fromEntries(
              Object.entries(rest).map(([key, value]) => [
                typeof key === 'string' ? key?.trim()?.toLowerCase() : key,
                value ? String(value) : ''
              ])
            )
          };
        });

      if (!isEmpty(allUsers)) {
        const userChunks = chunk(allUsers, 100);
        let allSuccess = [];
        let allFailed = [];

        // ✅ Initialize progress
        setOpenModal(true);
        setIsUploading(true);
        setProgress({ uploaded: 0, success: 0, error: 0 });

        for (let i = 0; i < userChunks.length; i++) {
          const payload = {
            arazCityID: arazCity,
            miqaatID: miqaat,
            overwriteRecords,
            overwriteKGGroup,
            overwriteKGType,
            overwritePermission,
            users: userChunks[i]
          };

          try {
            const response = await dispatch(assignKGUserAction(payload));

            const success = get(response, 'payload.data.success', []);
            const failedRaw = get(response, 'payload.data.failed', []);
            const itsIssueRaw = get(response, 'payload.data.ITSIssue', []);

            const formatFailures = (items) =>
              items?.map((item) => ({
                ITSID: item?.ITSID,
                reason: item?.reason || 'Something went wrong'
              }));

            const failed = formatFailures(failedRaw);
            const itsIssues = formatFailures(itsIssueRaw);

            allSuccess.push(...success);
            allFailed.push(...failed, ...itsIssues);

            if (!get(response, 'payload.success', false)) {
              allFailed.push(
                ...userChunks[i].map((u) => ({
                  ITSID: u.ITSID,
                  reason: u?.reason || `Something went wrong`
                }))
              );
            }

            // 🔄 Update progress
            setProgress((prev) => ({
              uploaded: prev.uploaded + userChunks[i].length,
              success: prev.success + success.length,
              error: prev.error + failed.length + itsIssues.length
            }));
          } catch (err) {
            const fallbackErrors = userChunks[i].map((u) => ({
              ITSID: u?.ITSID,
              reason: u?.reason || `Some thing went wrong',`
            }));

            allFailed.push(...fallbackErrors);

            setProgress((prev) => ({
              uploaded: prev?.uploaded + userChunks?.[i]?.length,
              success: prev?.success,
              error: prev?.error + fallbackErrors?.length
            }));
          }
        }

        // ✅ After all chunks processed
        setIsUploading(false);
        setTimeout(() => {
          setOpenModal(false); // 👈 Auto-close modal
        }, 1000); // Optional delay for better UX

        // ✅ Reset form & state
        reset({ ...AssignRoleByFileDefaultValues, miqaat, arazCity });
        setResults({ success: allSuccess, failed: allFailed });
        setFileName('');
        setFileData([]);
      } else {
        dispatch(notification(false, 'File format is invalid', true));
      }
    }
  };

  const handleCancel = () => {
    if (steps > 1) {
      setSteps(steps - 1);
    }
    setHeaderValues(defaultHeaderValues);
  };

  const handleTableInput = async (rowData, value, name) => {
    let updatedITSData = await updateTableData(fileData, rowData, name, value, kgData, departmentData?.departments, dispatch);
    setFileData(updatedITSData);
  };

  const getOptions = (options, key) => {
    return options?.map((item) => ({ value: item?._id, label: item[key] }));
  };

  const isLoading = () => {
    return functionsData?.loading || kgData?.loading;
  };

  const handleChangeAllRows = async (name, value, singleRowName) => {
    updateAllRows(name, value, singleRowName, {
      kgData,
      departmentData,
      dispatch,
      getFunctionsByDepartmentAction,
      setHeaderValues,
      setAllITSID: setFileData,
      allITSID: fileData,
      headerValues,
      getOptions
    });
  };

  const headers = [
    {
      name: 'logo',
      type: 'image',
      title: '',
      sortingactive: true
    },
    {
      name: 'ITSID',
      type: 'text',
      title: 'ITS ID',
      sortingactive: true,
      width: '100px'
    },
    {
      name: 'name',
      type: 'text',
      title: 'Name',
      sortingactive: true
    },
    {
      name: 'jamiatID.name',
      type: 'text',
      title: 'Jamiat',
      sortingactive: true
    },
    {
      name: 'jamaatID.name',
      type: 'text',
      title: 'Jamaat',
      sortingactive: true
    },
    {
      name: 'errorMessage',
      type: 'info',
      title: 'Error',
      sortingactive: true
    },
    {
      name: 'actions',
      type: 'input',
      title: 'Int. plug-in',
      sortingactive: false,
      component: ActionButtons,
      minWidth: '150px',
      titleType: 'checkbox',
      inputName: 'allInternationalPlugin',
      onChange: (e) => {
        handleChangeAllRows('allInternationalPlugin', e.target.checked, 'isInternationalPlugin');
      },
      multipleButtons: [
        {
          type: 'checkbox',
          value: '',
          name: 'isInternationalPlugin',
          showButton: 'isUserFound',
          handleInputChange: async (type, name, value, rowData) => {
            await handleTableInput(rowData, value, name);
          }
        }
      ]
    },
    {
      name: 'actions',
      type: 'input',
      title: 'Consent',
      sortingactive: false,
      component: ActionButtons,
      minWidth: '100px',
      titleType: 'checkbox',
      inputName: 'allConsentRequired',
      onChange: (e) => {
        handleChangeAllRows('allConsentRequired', e?.target?.checked, 'consentRequired');
      },
      multipleButtons: [
        {
          type: 'checkbox',
          value: '',
          name: 'consentRequired',
          showButton: 'isUserFound',
          handleInputChange: async (type, name, value, rowData) => {
            await handleTableInput(rowData, value, name);
          }
        }
      ]
    },
    {
      name: 'actions',
      type: 'input',
      title: '',
      sortingactive: false,
      component: ActionButtons,
      minWidth: '410px',
      titleType: 'multipleInput',
      className: 'assign-kg-select-field',
      multipleHeaderInput: [
        {
          type: 'select',
          placeholder: 'KG Group',
          name: 'allKGGroup',

          className: 'assign-kg-select-field',
          size: 'small',
          items: getOptions(kgData?.allKGGroup, 'name'),
          onChange: (type, value, name) => {
            handleChangeAllRows('allKGGroup', value, 'kgGroup');
          }
        },
        {
          type: 'select',
          placeholder: 'KG Type',
          name: 'allKGType',

          className: 'assign-kg-select-field',
          size: 'small',
          items: getOptions(kgData?.allKGType, 'name'),
          onChange: (type, value, name) => {
            handleChangeAllRows('allKGType', value, 'kgType');
          }
        },
        {
          type: 'select',
          placeholder: 'Position',
          name: 'allPosition',

          className: 'assign-kg-select-field',
          size: 'small',
          items: getOptions(kgData?.allHierarchyPosition, arazCityData.showPositionAlias ? 'alias' : 'name'),
          onChange: (type, value, name) => {
            handleChangeAllRows('allPosition', value, 'hierarchyPosition');
          }
        },
        {
          type: 'select',
          placeholder: 'Department',
          name: 'allDepartment',
          // showButton: 'isDepartmental',
          className: 'assign-kg-select-field',
          size: 'small',
          items: getOptions(departmentData?.departments, 'name'),
          onChange: (type, value, name) => {
            handleChangeAllRows('allDepartment', value, 'department');
          }
        },
        {
          type: 'select',
          placeholder: 'Function',
          name: 'allFunction',
          // showButton: 'isDepartmental',
          className: 'assign-kg-select-field',
          dynamicOptions: 'functionsData',
          size: 'small',
          items: getOptions(functionsData?.functionsForDepartment, 'name'),
          onChange: (type, value, name) => {
            handleChangeAllRows('allFunction', value, 'function');
          }
        },
        {
          type: 'text',
          placeholder: 'Other',
          name: 'otherFunction',
          // showButton: 'isOtherFunction',
          className: 'assign-kg-select-field',
          size: 'small',
          onChange: (type, value, name) => {
            handleChangeAllRows('otherFunction', value, 'otherFunction');
          }
        },
        {
          type: 'select',
          placeholder: 'Permission',
          name: 'allPermission',
          className: 'assign-kg-select-field',
          size: 'small',
          items: getOptions(systemData?.systemRoles, 'name'),
          onChange: (type, value, name) => {
            handleChangeAllRows('allPermission', value, 'cityRoleID');
          }
        },
        {
          type: 'select',
          placeholder: 'Zone',
          name: 'allZone',
          // showButton: 'isZonal',
          className: 'assign-kg-select-field',
          size: 'small',
          items: getOptions(zoneData?.zoneForSingleArazCity, 'name'),
          onChange: (type, value, name) => {
            handleChangeAllRows('allZone', value, 'zone');
          }
        }
      ],
      multipleButtons: [
        {
          type: 'select',
          placeholder: 'KG Group',
          name: 'kgGroup',
          showButton: 'isUserFound',
          className: 'assign-kg-select-field',
          size: 'small',
          items: getOptions(kgData?.allKGGroup, 'name'),
          handleInputChange: async (type, name, value, rowData) => {
            await handleTableInput(rowData, value, name);
          }
        },
        {
          type: 'select',
          placeholder: 'KG Type',
          name: 'kgType',
          showButton: 'isUserFound',
          className: 'assign-kg-select-field',
          size: 'small',
          items: getOptions(kgData?.allKGType, 'name'),
          handleInputChange: async (type, name, value, rowData) => {
            await handleTableInput(rowData, value, name);
          }
        },
        {
          type: 'select',
          placeholder: 'Position',
          value: '',
          showButton: 'isUserFound',
          className: 'assign-kg-select-field',
          size: 'small',
          name: 'hierarchyPosition',
          items: getOptions(kgData?.allHierarchyPosition, arazCityData.showPositionAlias ? 'alias' : 'name'),
          handleInputChange: async (type, name, value, rowData) => {
            await handleTableInput(rowData, value, name);
          }
        },
        {
          type: 'select',
          placeholder: 'Department',
          value: '',
          className: 'assign-kg-select-field',
          size: 'small',
          // showButton: 'isDepartmental',
          name: 'department',
          items: getOptions(departmentData?.departments, 'name'),
          handleInputChange: (type, name, value, rowData) => {
            handleTableInput(rowData, value, name);
          }
        },
        {
          type: 'select',
          placeholder: 'Function',
          value: '',
          className: 'assign-kg-select-field',
          size: 'small',
          // showButton: 'isDepartmental',
          name: 'function',
          itemName: 'functionsData',
          items: [],
          handleInputChange: async (type, name, value, rowData) => {
            await handleTableInput(rowData, value, name);
          }
        },
        {
          type: 'text',
          placeholder: 'Other',
          name: 'otherFunction',
          // showButton: 'isOtherFunction',
          className: 'assign-kg-select-field',
          size: 'small',
          handleInputChange: async (type, name, value, rowData) => {
            await handleTableInput(rowData, value, name);
          }
        },
        {
          type: 'select',
          placeholder: 'System Permission',
          value: '',
          className: 'assign-kg-select-field',
          size: 'small',
          showButton: 'isUserFound',
          name: 'cityRoleID',
          items: getOptions(systemData?.systemRoles, 'name'),
          handleInputChange: (type, name, value, rowData) => {
            handleTableInput(rowData, value, name);
          }
        },
        {
          type: 'select',
          placeholder: 'Zone',
          value: '',
          showButton: 'isZonal',
          className: 'assign-kg-select-field',
          size: 'small',
          name: 'zone',
          items: getOptions(zoneData?.zoneForSingleArazCity, 'name'),
          handleInputChange: async (type, name, value, rowData) => {
            await handleTableInput(rowData, value, name);
          }
        }
      ]
    },
    {
      name: 'actions',
      type: 'actions',
      title: 'Actions',
      sortingactive: false,
      component: ActionButtons,
      multipleButtons: [
        {
          type: 'button',
          showButton: 'isUserFound',
          buttonTitle: 'Save',
          action: 'save',
          buttonOnClick: async (type, rowData) => {
            await handleSingleKGSubmit({
              data: rowData,
              arazCityData,
              miqaatID: getValues()?.miqaat,
              arazCityID: getValues()?.arazCity,
              dispatch,
              departmentData,
              zoneData,
              functionsData,
              allITSID: fileData,
              kgData,
              handleCancel,
              setAllITSID: setFileData
            });
          },
          color: 'primary',
          tooltip: 'Save'
        },
        {
          type: 'button',
          showButton: 'isUserFound',
          buttonTitle: 'Delete',
          action: 'save',
          buttonOnClick: async (type, rowData) => {
            setDeleteRow({ ...rowData });
          },
          color: 'error',
          tooltip: 'Delete'
        }
      ]
    }
  ];

  const handleCloseDeleteModal = () => {
    setDeleteRow({});
  };

  const handleCloseExistModal = () => {
    setAlreadyExists(0);
  };

  const handleDeleteRow = () => {
    let updatedRow = fileData?.filter((item) => item?.ITSID !== deleteRow?.ITSID);
    setFileData(updatedRow);
    setDeleteRow({});
  };

  const handleRemoveExistModal = () => {
    setAlreadyExists(0);
    let updatedRow = fileData?.filter((item) => item?.isAlreadyExists !== true);
    setFileData(updatedRow);
  };

  const alreadyExistsModalButtons = [
    { label: 'Override', onClick: () => handleCloseExistModal(), color: 'error', disabled: isLoading() },
    { label: 'Proceed', onClick: () => handleRemoveExistModal(), variant: 'contained', color: 'primary', disabled: isLoading() }
  ];

  const DeleteModalButtons = [
    { label: 'Cancel', onClick: () => handleCloseDeleteModal(), color: 'error', disabled: isLoading() },
    { label: 'Confirm', onClick: () => handleDeleteRow(), variant: 'contained', color: 'primary', disabled: isLoading() }
  ];

  const getTotalFiles = () => {
    let fileData = filterFileData(getValues()?.file);
    return fileData?.length;
  };

  const handleCloseResults = () => {
    setResults({});
  };

  return (
    <>
      <CustomModal
        borderRadius="20px"
        Component={<ImportITS progress={progress} total={getTotalFiles()} loading={isLoading()} />}
        open={openModal}
      />
      {alreadyExists > 0 && (
        <CustomModal
          borderRadius="20px"
          buttons={alreadyExistsModalButtons}
          Component={
            <>
              <AlreadyAssignedUsers userCount={alreadyExists} />
            </>
          }
          open={alreadyExists > 0}
        />
      )}

      {!isEmpty(deleteRow) && (
        <CustomModal
          cancel={handleCloseDeleteModal}
          buttons={DeleteModalButtons}
          borderRadius="20px"
          Component={<ConfirmationModal title="Row" />}
          open={true}
        />
      )}

      {!isEmpty(results) && (
        <CustomModal
          cancel={handleCloseResults}
          borderRadius="20px"
          Component={<ImportResults results={results} handleCloseResults={handleCloseResults} />}
          open={!isEmpty(results) ? true : false}
        />
      )}

      {steps === 1 ? (
        <CustomForm
          loading={kgData?.loading}
          title={'Assign Hierarchy Position (File Import)'}
          fields={[
            {
              type: 'component',
              Component: () => (
                <Typography
                  variant="body1"
                  onClick={() => {
                    const data = [
                      {
                        ITS_ID: '30351956',
                        Department: 'HR',
                        Function: 'HR Analytics',
                        Zone: 'CMZ',
                        Designation: 'Zone Team',
                        KGType: 'Local KG',
                        KGGRoup: 'Zonal',
                        Permission: 'Default'
                      }
                    ];
                    const worksheet = XLSX.utils.json_to_sheet(data);
                    const workbook = XLSX.utils.book_new();
                    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');
                    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
                    const blob = new Blob([excelBuffer], { type: 'application/octet-stream' });

                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = 'MiqaatHR to AMS Upload - 21st June 2025 - XLSX.xlsx';
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);
                  }}
                  sx={{
                    padding: '8px 16px',
                    borderRadius: '8px',
                    cursor: 'pointer',
                    backgroundColor: '#f0f0f0',
                    display: 'inline-block',
                    '&:hover': {
                      backgroundColor: '#e0e0e0',
                      textDecoration: 'underline'
                    },
                    fontWeight: 500
                  }}
                >
                  📥 Download Sample File
                </Typography>
              ),
              componentProps: {
                hideSearchField: true,
                xl: 6,
                md: 6,
                xs: 12
              }
            },

            {
              type: 'component',
              Component: SelectMiqaatAndArazCity,
              componentProps: {
                hideSearchField: true,
                values: getValues(),
                xl: 6,
                md: 6,
                xs: 12,
                errors: errors,
                onSelectChange: (updatedValues) => onSelectChange(updatedValues)
              }
            },
            ...AssignRoleByFileFields
          ]}
          fieldValues={getValues()}
          control={control}
          fileName={fileName}
          errors={errors}
          handleInputChange={(type, name, value, fileName) => handleInputChange(type, name, value, fileName)}
          handleSubmit={handleSubmit(onSubmit)}
          submitButtonLabel="Submit"
          hideCancelButton={true}
          inputFieldWrapper="assign-kg-form-fields"
        />
      ) : (
        <Box className="import-file-table-wrapper ">
          {isLoading() && <Loader />}
          <PageTitle title={'Assign Hierarchy Position (File Import)'} handleCancel={handleCancel} />
          <div className="form-main-card bulk-import">
            <TableComponent
              tableContainerClassName={'import-file-table sticky-table-container'}
              loading={kgData?.loading}
              columns={headers}
              headerValues={headerValues}
              rowTooltip="errorMessage"
              rows={fileData}
              enablePagination={true}
              headerClassName="sticky-table-head"
            />
            <UserAlreadyExistsIndicator />
            <Grid item xs={12} className="import-file-form-submit-button">
              <FormButtons handleSubmit={() => onSubmit()} submitButtonLabel={'Submit'} onCancel={handleCancel} loading={kgData?.loading} />
            </Grid>
          </div>
        </Box>
      )}
    </>
  );
};
export default AssignHierarchyPositionFile;
